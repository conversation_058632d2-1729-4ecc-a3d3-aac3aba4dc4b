import type { FormInstance } from 'antd'
import type { <PERSON><PERSON>ser, <PERSON><PERSON>ield, WizardFormModel, WizardStep } from '../model'
import { getFieldByName } from '../helpers'
import { useDocuments } from '../hooks/documents'
import { useLeaniX } from '../hooks/leanix'
import { getName, handleExtAppDeepLink, handleExternalApps, lifecycleOrderControl } from './helpers'

const { addRessource } = useDocuments()
const { saveApplicationWithAllData, getFactSheetByNameAndType } = useLeaniX()

export const addApplicationForm: WizardFormModel = {
  id: 'addApplication',
  name: 'Add Application',
  originFactSheet: 'Application',
  addWithTemporaryMode: true,
  onlyAdmin: false,
  saveErrorMessage: 'Error while saving the application. Please try again',
  saveSuccessMessage: 'Application successfully created',
  steps: [
    {
      name: 'Information',
      rules: [
        // WHEN Sourcing == saas -> add Fields
        (form, steps, currentStep, currentUser, update) => {
          const value: string | undefined = form.getFieldValue('sourcing')
          if (value) {
            const newSteps: WizardStep[] = [...steps]
            const fieldChange1 = getFieldByName(newSteps, currentStep, 'saaSClassification')
            const fieldChange2 = getFieldByName(newSteps, currentStep, 'saaSClassificationComment')
            fieldChange1.visible = value == 'saas'
            fieldChange2.visible = value == 'saas'

            newSteps[currentStep].fields = newSteps[currentStep].fields.map(f =>
              f.name === 'saaSClassification' ? fieldChange1 : f.name === 'saaSClassificationComment' ? fieldChange2 : f
            )
            update(newSteps)
          }
        },
        // WHEN softwaretype === thirdParty ->make software/service providers field required
        (form, steps, currentStep, currentUser, update) => {
          const value: string | undefined = form.getFieldValue('softwareType')
          if (value) {
            const newSteps: WizardStep[] = [...steps]
            const fieldChange = getFieldByName(newSteps, 1, 'relApplicationToSoftwareServiceProvider')
            fieldChange.required = value === 'thirdParty'
            newSteps[1].fields = newSteps[1].fields.map(f => f.name === 'relApplicationToSoftwareServiceProvider' ? fieldChange : f)
            update(newSteps)
          }
        },

        (form, steps, currentStep, currentUser, update, deepLinkArgs) => {
          handleExtAppDeepLink(form, steps, update, deepLinkArgs)
        },

        // External Applications
        (form, steps, currentStep, currentUser, update) => {
          handleExternalApps(form, steps, update, false)
        }
      ],
      fields: [
        {
          title: 'External Application',
          name: 'external',
          fieldType: 'Switch',
          description: `Placeholder for external partners or partner applications to depict interfaces 
                                  between Helvetia systems and these external applications. They normally do not hold Helvetia 
                                  data and are not actively used by Helvetia employees. If software licenses or SaaS subscriptions 
                                  are acquired, it is generally not considered an external partner application.`,
          required: true,
          visible: true
        },
        {
          title: 'Prefix',
          name: 'prefix',
          fieldType: 'Hidden',
          description: 'just the prefix',
          required: true,
          visible: false
        },
        {
          title: 'Name',
          name: 'name',
          fieldType: 'NameField',
          description: 'The name is used to identify this Fact Sheet in the Inventory, Reporting and Search.',
          required: true,
          visible: true
        },
        {
          title: 'Description',
          name: 'description',
          fieldType: 'TextArea',
          description: 'Please provide a meaningful description to enable other users to understand the main purpose.',
          required: true,
          visible: true
        },
        {
          title: 'Platform',
          name: 'plattform',
          fieldType: 'SingleSelect',
          description: 'Used to indicate the logical Helvetia platform that an Application or IT Component belongs to.',
          required: false,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'Sourcing',
          name: 'sourcing',
          fieldType: 'SingleSelect',
          description: 'Type of internal, public cloud or external hosting.',
          required: true,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'Saas Classification',
          name: 'saaSClassification',
          fieldType: 'SingleSelect',
          description: 'Classification based on SaaS Framework (Individual = no integration, cost < 2.5k, only public & internal data;'
            + ' Common = User < 100, cost < 200k p.a.a, no FINMA relevant data; Strategic = User > 100, cost > 200k p.a. o., or FINMA relevant data)',
          required: false,
          loadLeanIXOptions: true,
          visible: false
        },
        {
          title: 'SaaS Classification Comment',
          name: 'saaSClassificationComment',
          fieldType: 'TextArea',
          description: 'Any additional comments',
          required: false,
          visible: false
        },

        {
          title: 'Software Type',
          name: 'softwareType',
          fieldType: 'SingleSelect',
          description: '3rd Party Software and Individual Software.',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          title: 'GUI Types',
          name: 'guiType',
          fieldType: 'MultiSelect',
          description: '',
          required: true,
          loadLeanIXOptions: true,
          visible: true
        },
        {
          title: 'URL',
          name: 'url',
          fieldType: 'Text',
          description: 'URL to access the application.',
          required: false,
          addonBefore: 'https://',
          visible: true
        }
      ],
      customChecks: async (form: FormInstance, currentUser: ReportUser, step: WizardStep) => {
        const plainName = (form.getFieldValue('name') || '').toLowerCase()
        if (plainName.includes('(ext)') || plainName.includes('(ext.)')) {
          return 'We noticed that you included (ext) or (ext.) in the name. Please select External Application instead of adding your own extension. '
            + 'When you select External Application, the suffix is automatically applied.'
        }

        const name = getName(form.getFieldValue('prefix'), form.getFieldValue('name'), form.getFieldValue('external'))

        // check if already exists
        const data = await getFactSheetByNameAndType(name, 'Application')

        if (data.length > 0) {
          return 'Application name already exists'
        }
        return undefined
      }
    },
    {
      name: 'Provider',
      rules: [],
      fields: [
        {
          name: 'hostingProviderCountry',
          title: 'Hosting Provider Country',
          description: '',
          required: false,
          fieldType: 'MultiSelect',
          loadLeanIXOptions: true,
          visible: true
        },
        {
          name: 'relApplicationToHostingProvider',
          title: 'Hosting Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        },
        {
          name: 'relApplicationToOperationsProvider',
          title: 'Operations Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        },
        {
          name: 'relApplicationToSoftwareServiceProvider',
          title: 'Software/Service Providers',
          description: '',
          required: false,
          fieldType: 'MultiSelectProvider',
          loadFactSheet: 'ProviderId',
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Lifecycle',
      rules: [],
      fields: [
        {
          name: 'phaseIn',
          title: 'Phase in',
          description: 'Date since when the Application is in the phase of being built or acquired',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'active',
          title: 'Go-Live',
          description: 'Date since when the Desktop Software is productive and in use',
          fieldType: 'DatePicker',
          required: false,
          visible: true

        },
        {
          name: 'phaseOut',
          title: 'Switch Off',
          description: 'Date since when the Application is in the phase of being retired',
          fieldType: 'DatePicker',
          required: false,
          visible: true
        }
      ],
      customChecks: async (form) => {
        const phaseIn = form.getFieldValue('phaseIn')
        const active = form.getFieldValue('active')
        const phaseOut = form.getFieldValue('phaseOut')

        return lifecycleOrderControl(phaseIn, active, phaseOut)
      }
    },
    {
      name: 'Application Domain',
      rules: [],
      fields: [
        {
          name: 'applicationDomain',
          title: 'Application Domain',
          description: 'Which domain is the application assigned to?',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadFactSheet: 'Domain'
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Business Capabilities',
      rules: [],
      fields: [
        {
          name: 'businessCapabilities',
          title: 'Business Capabilities',
          description: 'Which Business Capabilities are supported by this Application?',
          fieldType: 'MultiSelect',
          required: false,
          visible: true,
          loadFactSheet: 'BusinessCapability'
        }
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Business Area',
      rules: [],
      fields: [
        {
          name: 'businessAreas',
          title: 'Business Areas',
          description: 'Please choose whether this Business Area is owning or using this Application (only one owner allowed)',
          fieldType: 'BusinessAreaSelection',
          required: true,
          visible: true,
          loadFactSheet: 'UserGroup'
        }
      ],
      customChecks: async (form: FormInstance, currentUser: ReportUser, step: WizardStep) => {
        const areaOwner: string | undefined = form.getFieldValue('businessAreaOwner')
        const areaUsers: string[] | undefined = form.getFieldValue('businessAreaUsers')

        if (areaOwner && areaUsers?.includes(areaOwner)) {
          return 'Business areas can\'t be defined be user and owner. Please change it'
        }

        return undefined
      }
    },
    {
      name: 'User Administration',
      rules: [],
      fields: [
        {
          name: 'userAdmin',
          title: 'User Administration',
          description: 'Specification of the user administration mechanism',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'commentOnUserAdmin',
          title: 'Comment on User Administration',
          description: 'Optional comment on user administration',
          fieldType: 'TextArea',
          required: false,
          visible: true
        },
        {
          name: 'userAccessControl',
          title: 'User Access Control',
          description: 'Specification of the access control mechanism',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'commentOnUserAccessControl',
          title: 'Comment on User Access Control',
          description: 'Optional comment on user access control',
          fieldType: 'TextArea',
          required: false,
          visible: true
        },
        {
          name: 'reachableFromNonManagedDevice',
          title: 'Application is reachable from a non-helvetia managed device',
          description: 'Indication whether the application is reachable from a non-helvetia managed device',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'multiFactorAuthentication',
          title: 'Multi-Factor Authentication',
          description: 'Indication whether the application has Multi-Factor implemented',
          fieldType: 'SingleSelect',
          required: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'multiFactorAuthenticationComment',
          title: 'Comment for used MFA type',
          description: 'Optional Comment on Multi-Factor Authentication',
          fieldType: 'TextArea',
          required: false,
          visible: true
        }
        /* {
                    name: "needsMFA",
                    title: "Application needs to implement multi-factor authentication",
                    description: "Indication whether the application needs multi-factor authentication (used for specific use-cases e.g. internal and external access with different authentication type)",
                    fieldType: "SingleSelect",
                    required: true,
                    visible: true,
                    loadLeanIXOptions: true,
                } */
      ],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Subscriptions',
      rules: [],
      fields: [{
        name: 'applicationOwnerTechnical',
        title: 'Application Owner (technical)',
        description: 'The Application Owner (technical) is responsible for maintenance, further development rsp. configuration as well as for the 2nd level support of the relevant application. He ensures that '
          + 'the business and technical requirements are implemented and that a reliable and costefficient '
          + 'operation is ensured in accordance with the business requirements and agreements.',
        fieldType: 'UserSelectMultiple',
        required: true,
        visible: true
      }, {
        name: 'applicationResponsibleFunctional',
        title: 'Application Responsible (functional)',
        description: 'The Application Responsible (functional) assumes functional responsibility for the application. '
          + 'This includes the definition and prioritization of requirements for the further development '
          + 'and improvements of the application. The definition of the security requirements, the rules '
          + 'for assigning access authorizations, and the assessment of business criticality are also his '
          + 'responsibility.',
        fieldType: 'UserSelectMultiple',
        required: true,
        visible: true
      }],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Summary',
      rules: [
        (form, steps, currentStep, currentUser, update) => {
          const newSteps: WizardStep[] = [...steps]

          const expected = getFieldByName(newSteps, currentStep, 'expectedSteps')

          expected.visible = currentUser.admin

          newSteps[currentStep].fields = newSteps[currentStep].fields.map((f) => {
            if (f.name === 'expectedSteps') { return expected }
            return f
          })

          update(newSteps)
        }
      ],
      fields: [
        {
          name: 'comment',
          title: 'Comment',
          description: 'Please mention here if you have any comments.',
          fieldType: 'TextArea',
          required: false,
          visible: true
        },
        {
          name: 'expectedSteps',
          title: 'Changes in LeanIX',
          description: '',
          required: false,
          fieldType: 'ExpectedSteps',
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    }
  ],
  init: (form, currentUser) => {
    // console.log("SAVE");
    form.setFieldValue('prefix', 'CH-')
    form.setFieldValue('external', false)
  },
  save: async (fsData, currentUser) => {
    // console.log("SAVE", fsData, currentUser)

    // TEMPORARY AS Resource
    const dataToSave = {
      ...fsData,
      creator: currentUser,
      fsType: 'Application'
    }

    const doc = await addRessource(`${fsData.prefix}${fsData.name}${fsData.external ? ' (ext.)' : ''}`, JSON.stringify(dataToSave))
    if (doc) {
      return 'temporarySaved'
    }
    else {
      return undefined
    }
  },

  saveDirect: async (fsData, currentUser) => {
    // console.log("save direct", fsData, currentUser);
    const result = await saveApplicationWithAllData(fsData)
    if (result.length > 0) { return result }
    else { return undefined }
  }
}
